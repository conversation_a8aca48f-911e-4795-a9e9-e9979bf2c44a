#[==============================================[
FindLibUSB
-----------

Searching libusb-1.0 library and creating imported 
target LibUSB::LibUSB

#]==============================================]

# TODO Append parts for Version compasion and REQUIRED support

# Removed early return for Windows to allow libusb detection

if (NOT TARGET LibUSB::LibUSB)
  # Try pkg-config first (Linux/Unix)
  find_package(PkgConfig QUIET)
  if(PkgConfig_FOUND)
    pkg_check_modules(LibUSB QUIET libusb-1.0)
  endif()

  # If pkg-config didn't work, try manual search (Windows)
  if(NOT LibUSB_FOUND)
    find_path(LibUSB_INCLUDE_DIR
      NAMES libusb.h
      PATHS
        ${CMAKE_PREFIX_PATH}/include
        ${CMAKE_PREFIX_PATH}/include/libusb-1.0
        /usr/include/libusb-1.0
        /usr/local/include/libusb-1.0
        /opt/local/include/libusb-1.0
        "C:/Program Files/libusb/include"
        "C:/Program Files (x86)/libusb/include"
        ENV LIBUSB_ROOT_DIR
      PATH_SUFFIXES libusb-1.0
    )

    find_library(LibUSB_LIBRARY
      NAMES usb-1.0 libusb-1.0 usb
      PATHS
        ${CMAKE_PREFIX_PATH}/lib
        /usr/lib
        /usr/local/lib
        /opt/local/lib
        "C:/Program Files/libusb/lib"
        "C:/Program Files (x86)/libusb/lib"
        ENV LIBUSB_ROOT_DIR
      PATH_SUFFIXES lib
    )

    if(LibUSB_INCLUDE_DIR AND LibUSB_LIBRARY)
      set(LibUSB_FOUND TRUE)
      set(LibUSB_INCLUDE_DIRS ${LibUSB_INCLUDE_DIR})
      set(LibUSB_LIBRARIES ${LibUSB_LIBRARY})
      message(STATUS "libusb-1.0 found manually: ${LibUSB_LIBRARY}")
    endif()
  endif()

  if(LibUSB_FOUND)
    message(STATUS "libusb-1.0 found using pkgconfig")

    add_library(LibUSB::LibUSB
      UNKNOWN IMPORTED
    )
    if (DEFINED LibUSB_INCLUDE_DIRS AND NOT LibUSB_INCLUDE_DIRS STREQUAL "")
      set_target_properties(LibUSB::LibUSB PROPERTIES
        INTERFACE_INCLUDE_DIRECTORIES ${LibUSB_INCLUDE_DIRS}
      )
    endif()

    if(LibUSB_LIBRARIES)
      find_library(LibUSB_LIBRARY
        NAMES ${LibUSB_LIBRARIES}
        PATHS ${LibUSB_LIBDIR} ${LibUSB_LIBRARY_DIRS}
      )
      if(LibUSB_LIBRARY)
        set_target_properties(LibUSB::LibUSB PROPERTIES
          IMPORTED_LINK_INTERFACE_LANGUAGES "C"
          IMPORTED_LOCATION ${LibUSB_LIBRARY}
        )
      else()
        message(WARNING "Could not found libusb-1.0 library file")
      endif()
    endif()
  endif()
else()
  message(WARNING "libusb-1.0 could not be found using pkgconfig")
endif()
